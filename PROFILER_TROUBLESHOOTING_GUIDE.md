# Google Cloud Profiler Troubleshooting Guide

## 🔍 Problem: Empty Profiler Interface

You're seeing the "Welcome to Cloud Profiler!" screen because profiling data isn't being collected or displayed properly.

## 🎯 Root Cause Analysis

### Two Services in Staging Environment

GitLab has **TWO separate services** deployed in `gitlab-ai-framework-stage`:

1. **`********************`** - Main production staging service
2. **`dws-loadtest`** - Load testing service for performance analysis

Both services have profiling enabled, but they appear as **separate services** in Google Cloud Profiler.

### Configuration Analysis

**File**: `gitlab-ai-gateway/********************/profiling.py`
```python
def setup_profiling():
    if os.environ.get("DUO_WORKFLOW_GOOGLE_CLOUD_PROFILER__ENABLED") != "true":
        return

    try:
        googlecloudprofiler.start(
            service="********************",  # ← Service name in profiler
            service_version=os.environ.get("K_REVISION", "1.0.0"),
        )
    except (ValueError, NotImplementedError) as e:
        log_exception(e)
```

**Environment Variables**:
- **duo-workflow-svc**: `DUO_WORKFLOW_GOOGLE_CLOUD_PROFILER__ENABLED: true`
- **dws-loadtest**: `DUO_WORKFLOW_GOOGLE_CLOUD_PROFILER__ENABLED: true`
- **GCP Project**: `gitlab-ai-framework-stage` (both services)

## 🚀 Step-by-Step Troubleshooting

### Step 1: Check Service Selection in Profiler

1. **Go to Google Cloud Profiler**: https://console.cloud.google.com/profiler
2. **Select Project**: `gitlab-ai-framework-stage`
3. **Check Service Dropdown**: Look for these services:
   - `********************`
   - `dws-loadtest`
4. **Select the correct service** you want to profile

### Step 2: Verify Time Range

1. **Check time range selector** (top right)
2. **Look for recent activity** (last 1-7 days)
3. **Expand time range** if no recent data

### Step 3: Verify Service Status

Check if the services are actually running and receiving traffic:

**Questions for Slack**:
```
Hey team! 👋 I'm investigating the pydantic bottleneck in ******************** and trying to access Google Cloud Profiler data.

I have access to gitlab-ai-framework-stage project, but the profiler interface is empty. I can see we have two services configured:
- ******************** 
- dws-loadtest

Questions:
1. Which service should I be looking at for the pydantic bottleneck analysis?
2. Are both services currently running and receiving traffic in staging?
3. When was the last time profiling data was collected? (I need to set the right time range)
4. Is there a specific time period when load testing was performed that I should focus on?
5. Do I need any additional permissions to view profiler data for these services?

Context: I'm working on the performance optimization mentioned in issue #578158 and need to reproduce the flame graphs showing the bind_tools bottleneck.
```

### Step 4: Check Service Logs

If services aren't showing up, check the logs:

1. **Go to Cloud Logging**: https://console.cloud.google.com/logs
2. **Filter by service**:
   ```
   resource.type="cloud_run_revision"
   resource.labels.service_name="********************"
   ```
3. **Look for profiler initialization messages**

### Step 5: Verify Profiler Initialization

Look for these log messages:
```
Successfully started Google Cloud Profiler
```

Or error messages:
```
Failed to start Google Cloud Profiler: <error>
```

## 🎯 Expected Profiler Data

Once you find the right service and time range, you should see:

### Service Overview
- **CPU profiles** showing function call stacks
- **Memory profiles** showing allocation patterns
- **Time range selector** with available data points

### Flame Graph Navigation
1. **Select CPU profile type**
2. **Choose time range** when bottleneck occurred
3. **Look for these call stacks**:
   ```
   bind_tools
   ├── convert_to_anthropic_tool
   │   ├── convert_to_openai_tool
   │   │   ├── _format_tool_to_openai_function
   │   │   │   ├── tool_call_schema
   │   │   │   │   ├── _create_subset_model
   │   │   │   │   │   └── create_model ← 28.4ms bottleneck
   ```

### Search Terms in Profiler
- `bind_tools`
- `create_model`
- `convert_to_anthropic_tool`
- `_create_subset_model`
- `pydantic`

## 🔧 Alternative Approaches

### Option 1: Local Profiling

If staging profiler data isn't available, you can profile locally:

```bash
# Enable profiler in local development
export DUO_WORKFLOW_GOOGLE_CLOUD_PROFILER__ENABLED=true
export GOOGLE_CLOUD_PROJECT=your-local-project

# Run load test locally
cd gitlab-ai-gateway
poetry run ******************** &
# Run your load test script
```

### Option 2: Manual Profiling

Use Python's built-in profiler:

```python
# Add to ********************/server.py
import cProfile
import pstats

def profile_bind_tools():
    profiler = cProfile.Profile()
    profiler.enable()
    
    # Your bind_tools call here
    model.bind_tools(tools)
    
    profiler.disable()
    stats = pstats.Stats(profiler)
    stats.sort_stats('cumulative')
    stats.print_stats(20)  # Top 20 functions
```

### Option 3: Request Load Test Run

Ask the team to run a load test while you monitor the profiler:

**Slack Message**:
```
Could someone help me run a load test against ******************** in staging? 

I need to capture profiler data showing the pydantic bottleneck for performance optimization work. 

Ideally:
- 10-15 RPS for 2-3 minutes
- Using the existing k6 load test scripts
- While I monitor Google Cloud Profiler in real-time

This will help me reproduce the flame graphs showing the bind_tools performance issue.
```

## 📊 What You Should See

### Healthy Profiler Data
- **Multiple profile types**: CPU, Memory, Heap
- **Recent timestamps**: Within last few days
- **Call stack data**: Detailed function hierarchies
- **Performance metrics**: Time spent in each function

### Bottleneck Indicators
- **High CPU usage** in `bind_tools` calls
- **Deep call stacks** through LangChain conversion functions
- **Repeated patterns** showing the same expensive operations
- **Time percentages** showing 6%+ CPU in `create_model`

## 🎯 Success Criteria

You'll know you've found the right data when you see:

1. **Service appears** in profiler dropdown
2. **Recent profile data** (last 1-7 days)
3. **CPU profiles** with call stack details
4. **bind_tools function** visible in flame graphs
5. **Performance bottleneck** clearly identified

## 📞 Escalation Path

If you still can't access profiler data:

1. **Ask in #duo-workflow Slack channel**
2. **Tag service owners**: @dgriffith, @wortschi
3. **Request profiler access verification**
4. **Ask for recent load test timing**
5. **Consider alternative profiling approaches**

The key is identifying which service has the data and when it was collected!
