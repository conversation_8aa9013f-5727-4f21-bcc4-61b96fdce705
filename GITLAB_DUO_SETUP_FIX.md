# GitLab Duo Setup Fix - Comprehensive Guide

## Problem Summary

You're experiencing a **401 "Forbidden by auth provider"** error when running the GitLab Duo health check. This error occurs during the "System exchange" check, which tests end-to-end authentication between your GitLab instance and the AI Gateway.

## Root Cause Analysis

After deep investigation of the codebase, the issue stems from:

1. **Missing or Invalid License**: GitLab Duo requires an **Ultimate license with activation code** (cloud license)
2. **Token Generation Failure**: The `CloudConnector::Tokens.get` method needs:
   - A valid license to fetch active add-ons (`GitlabSubscriptions::AddOnPurchase`)
   - Proper environment variables for self-signed token generation
3. **Authentication Flow**: The system exchange test calls:
   ```
   EndToEndProbe → CodeSuggestionsClient.test_completion → 
   Gitlab::AiGateway.headers → CloudConnector::Tokens.get
   ```

## Solution Steps

### Step 1: Obtain a GitLab Ultimate License

**You MUST get a proper Ultimate license activation code.** Follow the official process:

1. **For GitLab Team Members**:
   - Follow the internal process at: https://docs.gitlab.com/development/ai_features/ai_development_license/
   - Request a **GitLab Self-Managed Ultimate** license (not a legacy license file)
   - You'll receive an **activation code** (a long string)

2. **For External Contributors**:
   - You may need to request access through GitLab's community channels
   - Or use the staging AI Gateway workaround (see Alternative Solution below)

### Step 2: Upload the License to Your GDK

Once you have the activation code:

1. Open your browser: **http://127.0.0.1:3000/admin/subscription**
2. Click "**Activate GitLab**" or "**Add License**"
3. Paste your **activation code**
4. Click "**Activate**"

### Step 3: Verify Environment Variables

Your `gdk.yml` is already correctly configured with:
```yaml
env:
  GITLAB_LICENSE_MODE: test  # ✅ Now enabled (was commented out)
  CUSTOMER_PORTAL_URL: https://customers.staging.gitlab.com  # ✅ Correct
  CLOUD_CONNECTOR_SELF_SIGN_TOKENS: 1  # ✅ Correct
```

And `env.runit` has:
```bash
export AI_GATEWAY_URL=http://127.0.0.1:5052  # ✅ Correct
```

### Step 4: Restart All Services

After uploading the license, restart your GDK:

```bash
gdk restart
```

Wait for all services to start (check with `gdk status`).

### Step 5: Verify the Setup

1. **Check License Status**:
   ```bash
   cd gitlab
   bundle exec rails runner "puts License.current&.plan || 'No license'; puts License.current&.cloud? || 'Not cloud license'"
   ```
   
   Expected output:
   ```
   ultimate
   true
   ```

2. **Check AI Gateway URL**:
   ```bash
   cd gitlab
   bundle exec rails runner "puts Gitlab::CurrentSettings.ai_gateway_url || 'Not set'"
   ```
   
   Expected output:
   ```
   http://127.0.0.1:5052
   ```

3. **Run Health Check**:
   - Go to: **http://127.0.0.1:3000/admin/gitlab_duo**
   - Click "**Run health check**"
   - All checks should pass, including "System exchange"

### Step 6: Assign Duo Seats (if needed)

If the health check passes but you still can't use Duo features:

1. Go to: **http://127.0.0.1:3000/admin/code_suggestions**
2. Ensure your user (root) has a Duo seat assigned
3. The GDK setup scripts usually assign a seat to the root user automatically

## Alternative Solution: Use Staging AI Gateway

If you can't get a license immediately and want to test quickly:

1. **Set the staging AI Gateway URL**:
   ```bash
   # Add to env.runit
   export DEVELOPMENT_AI_GATEWAY_URL=https://cloud.staging.gitlab.com
   ```

2. **Unset the local AI Gateway URL** (temporarily):
   ```bash
   # In Rails console
   cd gitlab
   bundle exec rails console
   # Then run:
   Ai::Setting.instance.update!(ai_gateway_url: nil)
   ```

3. **Restart GDK**:
   ```bash
   gdk restart
   ```

**Note**: This is only for testing. You'll still need a proper license for full functionality.

## Troubleshooting

### Issue: "No license" or "Not cloud license"

**Solution**: You need to upload a proper activation code (cloud license), not a legacy license file.

### Issue: Still getting 401 after uploading license

**Possible causes**:
1. **License doesn't include Duo add-ons**: Make sure your license includes Duo Pro or Duo Enterprise
2. **Environment variables not loaded**: Restart GDK completely: `gdk stop && gdk start`
3. **Multiple licenses**: Check if you have multiple licenses and remove old ones:
   ```bash
   cd gitlab
   bundle exec rails console
   # Check licenses
   License.all.each { |l| puts "ID: #{l.id}, Plan: #{l.plan}, Cloud: #{l.cloud?}" }
   # Remove old licenses (keep only the cloud license)
   License.find(OLD_ID).destroy
   ```

### Issue: AI Gateway not accessible

**Check AI Gateway status**:
```bash
curl http://127.0.0.1:5052/monitoring/healthz
```

Expected response:
```json
{"status":"healthy"}
```

If not working:
```bash
gdk restart gitlab-ai-gateway
tail -f log/gitlab-ai-gateway/current
```

### Issue: Environment variables not being read

**Verify environment variables are loaded**:
```bash
cd gitlab
bundle exec rails runner "puts ENV['CLOUD_CONNECTOR_SELF_SIGN_TOKENS']"
```

Expected output: `1`

If empty, the environment variables aren't being loaded. Try:
```bash
gdk reconfigure
gdk restart
```

## Technical Details (For Reference)

### Authentication Flow

1. **Health Check Initiated**: Admin clicks "Run health check" at `/admin/gitlab_duo`
2. **End-to-End Probe**: `CloudConnector::StatusChecks::Probes::EndToEndProbe` is executed
3. **Test Completion**: Calls `Gitlab::Llm::AiGateway::CodeSuggestionsClient.test_completion`
4. **Generate Headers**: `Gitlab::AiGateway.headers` is called to create auth headers
5. **Get Token**: `CloudConnector::Tokens.get` generates a JWT token
6. **Token Generation Logic**:
   - Checks if `CLOUD_CONNECTOR_SELF_SIGN_TOKENS=1` (line 53 in `ee/lib/cloud_connector/tokens.rb`)
   - If true, generates self-signed JWT with `TokenIssuer`
   - Fetches active add-ons from license: `GitlabSubscriptions::AddOnPurchase.for_active_add_ons`
   - **This requires a valid Ultimate license with Duo add-ons**
7. **Send Request**: Makes POST request to AI Gateway with JWT in Authorization header
8. **AI Gateway Validates**: AI Gateway validates the JWT and checks permissions
9. **Response**: Returns 200 if successful, 401 if authentication fails

### Why You Need a License

The `CloudConnector::Tokens.get` method calls `fetch_active_add_ons` which queries:
```ruby
GitlabSubscriptions::AddOnPurchase.for_active_add_ons(add_on_names, resource).uniq_add_on_names
```

Without a valid Ultimate license with Duo add-ons, this returns an empty array, and the AI Gateway rejects the token.

## Next Steps

1. **Get your activation code** from the GitLab team member license process
2. **Upload it** to your GDK at http://127.0.0.1:3000/admin/subscription
3. **Restart GDK**: `gdk restart`
4. **Run health check**: http://127.0.0.1:3000/admin/gitlab_duo
5. **Verify all checks pass** ✅

## Support

If you continue to have issues after following these steps:
- Check GitLab logs: `tail -f gitlab/log/development.log`
- Check AI Gateway logs: `tail -f log/gitlab-ai-gateway/current`
- For license issues: Reach out to #s_fulfillment_engineering in Slack
- For AI Gateway issues: Reach out to #g_ai_framework in Slack

