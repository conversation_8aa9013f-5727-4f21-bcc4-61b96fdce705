# frozen_string_literal: true

require 'net/http'
require 'json'
begin
  require 'tty-markdown'
rescue LoadError
end

module GDK
  class ClaudeConnector
    ANTHROPIC_MESSAGES_API_V1_URL = 'https://api.anthropic.com/v1/messages'

    ClaudeConnectorError = Class.new(StandardError)

    def initialize(out)
      @out = out
    end

    def call(prompt)
      if api_key.empty?
        out.warn('AI assistance for troubleshooting is missing the API key.')
        out.info('Add the API key in your shell config `export ANTHROPIC_API_KEY=<your-anthropic-api-key>')
        return
      end

      message = create_chat(messages(prompt))

      display_message(message)
      out.puts("The current conversation used #{used_tokens(message)} tokens so far.")

      if out.prompt('Was this answer helpful?').match?(/\Ay(?:es)*\z/i)
        out.puts('Great, we are happy you could resolve your problem!')
        return
      end

      follow_up_question = out.prompt('Please enter your follow-up question')
      if follow_up_question.empty?
        out.puts('Empty follow-up question provided. Closing chat.')
        return
      end

      out.info('Processing your follow-up question ...')

      follow_up_message = create_chat(messages(prompt) + follow_up(message_content(message), follow_up_question))

      display_message(follow_up_message)
      out.puts("The current conversation used #{used_tokens(follow_up_message)} tokens in total.")
    end

    private

    attr_accessor :out

    def create_chat(messages)
      uri = URI(ANTHROPIC_MESSAGES_API_V1_URL)
      request = Net::HTTP::Post.new(uri)
      request.content_type = 'application/json'
      request['x-api-key'] = api_key

      request.body = {
        max_tokens: 1024,
        messages: messages,
        model: 'claude-sonnet-4-20250514',
        system: system_prompt
      }.to_json

      request_options = { use_ssl: uri.scheme == 'https' }
      response = begin
        Net::HTTP.start(uri.hostname, uri.port, request_options) do |http|
          http.request(request)
        end
      rescue StandardError => e
        raise ClaudeConnectorError.new, "Failed to reach Anthropic server: #{e.message}"
      end

      begin
        JSON.parse(response.body)
      rescue StandardError => e
        raise ClaudeConnectorError.new, "There was an error while parsing the response from Anthropic: #{e.message}"
      end
    end

    def api_key
      @api_key ||= ENV.fetch('ANTHROPIC_API_KEY', '')
    end

    def display_message(message)
      return '' if message_content(message).empty?

      unless defined?(TTY::Markdown)
        out.puts(message_content(message))
        return
      end

      options = { width: 80, color: out.colorize? ? :always : :never }
      out.puts TTY::Markdown.parse(message_content(message), **options)
    end

    def messages(prompt)
      [
        { role: 'user', content: prompt }
      ]
    end

    def follow_up(assistant_response, follow_up_question)
      [
        { role: 'assistant', content: assistant_response },
        { role: 'user', content: follow_up_question }
      ]
    end

    def system_prompt
      [
        {
          type: 'text',
          text: 'You are a GitLab Development Kit (GDK) error diagnostics specialist. You will receive error messages and log outputs from users attempting to install or run GitLab locally.'
        },
        {
          type: 'text',
          text: <<~PROMPT
            Your role:
            - Analyze error messages to identify root causes
            - Provide specific, actionable solutions for Ruby, gem, and dependency issues
            - Offer multiple potential fixes when the exact cause is ambiguous
          PROMPT
        },
        {
          type: 'text',
          text: <<~PROMPT
            Response format:
              - Lead with the most likely solution
              - Include exact commands, file paths, and configuration changes
              - Provide alternative solutions if the primary fix may not apply
              - Be concise but complete - users cannot ask follow-up questions
          PROMPT
        },
        {
          type: 'text',
          text: <<~PROMPT
            Focus areas:
              - Ruby version and environment problems
              - Bundler and gem dependency conflicts
              - System dependencies (PostgreSQL, Redis, Node.js, Git)
              - GDK configuration and setup issues
              - Common installation and runtime errors
          PROMPT
        },
        {
          type: 'text',
          text: <<~PROMPT
            Assume users have basic command-line access but may need explicit file paths and command syntax.
            Never ask the user any questions, because this is not interactive!
          PROMPT
        }
      ]
    end

    def message_content(message)
      message['content'][0]['text']
    end

    def used_tokens(message)
      message['usage']['input_tokens'] + message['usage']['output_tokens']
    end
  end
end
