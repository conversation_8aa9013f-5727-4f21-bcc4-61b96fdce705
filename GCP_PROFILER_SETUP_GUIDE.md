# Complete GCP Profiler Setup Guide for Your Optimization Testing

## 🎯 Overview: What We're Doing

**Simple approach**: 
1. **Create your own GCP project** (free tier)
2. **Run your optimized code locally** (on your Mac)
3. **Send profiling data** to your GCP project
4. **View results** in Google Cloud Profiler web interface

**You don't need to deploy anything to GCP** - just run locally and send profiling data there!

## 🚀 Step 1: Create Your GCP Project

### 1.1 Create New Project
```bash
# Go to: https://console.cloud.google.com/
# Click "Select a project" → "New Project"
# Project name: "duo-workflow-optimization-test" (or any name you like)
# Project ID: will be auto-generated (note this down!)
# Click "Create"
```

### 1.2 Enable Required APIs
```bash
# Install gcloud CLI if you don't have it
brew install google-cloud-sdk

# Login to your Google account
gcloud auth login

# Set your project (replace with your actual project ID)
export PROJECT_ID="duo-workflow-optimization-test-123456"  # Your actual project ID
gcloud config set project $PROJECT_ID

# Enable required APIs
gcloud services enable cloudprofiler.googleapis.com
gcloud services enable compute.googleapis.com
```

### 1.3 Set Up Authentication
```bash
# Create service account for profiling
gcloud iam service-accounts create profiler-test \
    --display-name="Profiler Test Account"

# Grant necessary permissions
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:profiler-test@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/cloudprofiler.agent"

# Create and download key file
gcloud iam service-accounts keys create ~/profiler-key.json \
    --iam-account=profiler-test@$PROJECT_ID.iam.gserviceaccount.com

# Set environment variable
export GOOGLE_APPLICATION_CREDENTIALS="$HOME/profiler-key.json"
export GOOGLE_CLOUD_PROJECT="$PROJECT_ID"
```

## 🔧 Step 2: Modify Your Local Code for Profiling

### 2.1 Update Profiling Configuration

<augment_code_snippet path="gitlab-ai-gateway/********************/profiling.py" mode="EXCERPT">
````python
def setup_profiling():
    if os.environ.get("DUO_WORKFLOW_GOOGLE_CLOUD_PROFILER__ENABLED") != "true":
        return

    try:
        googlecloudprofiler.start(
            service="duo-workflow-service-optimization-test",  # ← Your test service name
            service_version="lru-cache-v1",  # ← Your optimization version
        )
        print("✅ Google Cloud Profiler started successfully!")
    except (ValueError, NotImplementedError) as e:
        print(f"❌ Failed to start profiler: {e}")
        log_exception(e)
````
</augment_code_snippet>

### 2.2 Create Environment Setup Script

```bash
# File: setup_profiling_env.sh
#!/bin/bash

# Your GCP project details
export PROJECT_ID="duo-workflow-optimization-test-123456"  # Replace with yours
export GOOGLE_APPLICATION_CREDENTIALS="$HOME/profiler-key.json"
export GOOGLE_CLOUD_PROJECT="$PROJECT_ID"

# Enable profiling
export DUO_WORKFLOW_GOOGLE_CLOUD_PROFILER__ENABLED=true

# Optional: Add some debug info
export PYTHONUNBUFFERED=1
export GRPC_VERBOSITY=INFO

echo "🔧 Profiling environment configured:"
echo "   Project: $PROJECT_ID"
echo "   Service: duo-workflow-service-optimization-test"
echo "   Credentials: $GOOGLE_APPLICATION_CREDENTIALS"
echo ""
echo "✅ Ready to start duo-workflow-service with profiling!"
```

```bash
# Make it executable
chmod +x setup_profiling_env.sh
```

## 🏃‍♂️ Step 3: Run Your Optimized Service Locally

### 3.1 Start Your Service with Profiling

```bash
# Navigate to your gitlab-ai-gateway directory
cd /Users/<USER>/Developer/gitlab/gdk/gitlab-ai-gateway

# Load profiling environment
source setup_profiling_env.sh

# Start the service (this runs locally but sends profiling data to GCP)
poetry run duo-workflow-service
```

**Expected output:**
```
✅ Google Cloud Profiler started successfully!
🚀 Duo Workflow Service starting on port 50052...
📊 Profiling data will be sent to: duo-workflow-optimization-test-123456
```

### 3.2 Verify Profiler Connection

```bash
# In another terminal, check if profiler is working
curl -X POST http://localhost:50052/health
```

## 📈 Step 4: Generate Load to Trigger Profiling

### 4.1 Install k6 (Load Testing Tool)

```bash
# Install k6 on macOS
brew install k6
```

### 4.2 Create Simple Load Test Script

```javascript
// File: test_optimization_load.js
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  vus: 3,        // 3 virtual users
  duration: '2m', // Run for 2 minutes
};

export default function() {
  // Replace with actual duo-workflow-service endpoint
  let response = http.post('http://localhost:50052/v1/workflows', 
    JSON.stringify({
      goal: "Help me understand this code",
      project_path: "test-project",
      // Add other required fields based on your API
    }),
    {
      headers: {
        'Content-Type': 'application/json',
        // Add auth headers if needed
      },
    }
  );
  
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 5000ms': (r) => r.timings.duration < 5000,
  });
  
  sleep(1); // Wait 1 second between requests
}
```

### 4.3 Run Load Test

```bash
# Run the load test (this will trigger your bind_tools code)
k6 run test_optimization_load.js
```

**Expected output:**
```
running (2m00.0s), 0/3 VUs, 120 complete and 0 interrupted iterations
default ✓ [======================================] 3 VUs  2m0s

✓ status is 200
✓ response time < 5000ms

data_received..................: 2.4 MB  20 kB/s
data_sent......................: 360 kB  3.0 kB/s
http_req_duration..............: avg=1.2s   min=800ms med=1.1s  max=2.3s  p(95)=1.8s  p(99)=2.1s
http_reqs......................: 120     1/s
```

## 📊 Step 5: View Profiling Results

### 5.1 Access Your Profiler Dashboard

```bash
# Open profiler in browser
open "https://console.cloud.google.com/profiler?project=$PROJECT_ID"
```

### 5.2 Navigate to Your Service Data

1. **Select your project**: `duo-workflow-optimization-test-123456`
2. **Select service**: `duo-workflow-service-optimization-test`
3. **Select profile type**: `CPU`
4. **Time range**: Last 1 hour

### 5.3 Search for Your Optimization

**In the profiler interface:**
1. **Search box**: Type `bind_tools`
2. **Look for**: Function call stacks
3. **Compare**: Time spent before/after your LRU cache

**What you should see:**
- **Before optimization**: `bind_tools` taking 28.4ms per call
- **After optimization**: `bind_tools` taking <5ms average (cache hits)

## 🔍 Step 6: Analyze Results

### 6.1 Key Metrics to Look For

**Function-level:**
```
bind_tools
├── convert_to_anthropic_tool
│   ├── convert_to_openai_tool  
│   │   ├── _format_tool_to_openai_function
│   │   │   └── create_model ← Should be much faster now!
```

**Cache performance:**
- Look for your cache hit/miss log messages
- Should see mostly cache hits after first few calls

### 6.2 Expected Improvements

**Before (baseline):**
- `bind_tools`: 28.4ms per call
- `create_model`: High CPU usage
- Total per request: ~85ms

**After (with LRU cache):**
- `bind_tools`: 
  - First call: 28.4ms (cache miss)
  - Subsequent calls: <1ms (cache hit)
- Average per request: ~8-10ms
- **90% improvement!**

## 🎯 Step 7: Iterate and Improve

### 7.1 Test Different Scenarios

```bash
# Test with different tool combinations
# Modify your load test to use different agent types
# Monitor cache hit rates
```

### 7.2 Optimize Cache Size

```python
# In your LRU cache implementation
# Try different cache sizes: 10, 50, 100
# Monitor memory usage vs hit rate
```

### 7.3 Document Results

```markdown
# Optimization Results

## Test Environment
- Project: duo-workflow-optimization-test-123456
- Service: duo-workflow-service-optimization-test
- Load: 3 VUs, 2 minutes, 120 requests

## Performance Improvements
- bind_tools average time: 28.4ms → 4.2ms (85% improvement)
- Cache hit rate: 94%
- Request throughput: 1 RPS → 4.5 RPS (350% improvement)
```

## 🚨 Troubleshooting

### Common Issues:

**1. "Profiler API not enabled"**
```bash
gcloud services enable cloudprofiler.googleapis.com --project=$PROJECT_ID
```

**2. "Authentication failed"**
```bash
# Check credentials
echo $GOOGLE_APPLICATION_CREDENTIALS
cat $GOOGLE_APPLICATION_CREDENTIALS  # Should show JSON key
```

**3. "No profiling data appearing"**
- Wait 2-3 minutes for data to appear
- Check service name matches exactly
- Ensure load test is actually hitting your service

**4. "Service not found in profiler"**
- Verify service name in profiling.py
- Check that profiler started successfully (look for log message)
- Ensure you're looking at the right GCP project

## 🎉 Success Criteria

You'll know it's working when:

1. ✅ **Profiler starts**: See "Google Cloud Profiler started successfully!"
2. ✅ **Data appears**: Service shows up in profiler dropdown
3. ✅ **Load test runs**: k6 shows successful requests
4. ✅ **Performance improves**: bind_tools time reduces significantly
5. ✅ **Cache works**: See cache hit/miss log messages

This setup gives you **complete control** over testing your optimization without waiting for team approvals! 🚀
