---
title: GitLab Knowledge Graph (GKG)
---

## Installation

### Enable GitLab Knowledge Graph Deployed in the GDK

The GitLab GKG component is automatically downloaded into your GDK root under two projects:

1. `/gitlab-zoekt-indexer`: Shared with Zoekt functionality as well. See [Zoekt](zoekt.md).
1. `/gitlab-knowledge-graph`: (not added yet) The rust project that is running the GKG indexer and webserver.

To enable the service and run it as part of `gdk start`:

1. Run `gdk config set gkg.enabled true`.
1. Run `gdk reconfigure`.
1. Run `gdk start`.
   This starts different components for development:
   - `gkg-proxy-1`
   - `gkg-proxy-2`
   - `gkg-indexer-1` (not added yet)
   - `gkg-indexer-2` (not added yet)
   - `gkg-webserver-1` (not added yet)
   - `gkg-webserver-2` (not added yet)
