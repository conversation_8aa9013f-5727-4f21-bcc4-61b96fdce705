---
title: User personas
---

> [!note]
> Parts of this page and the personas are adapted from [our handbook page](https://handbook.gitlab.com/handbook/product/personas/) about personas.

User personas are a generalized way of talking about the ideal target we
are aiming to design GDK for. They help us primarily to define our
product but also our messaging approach. Keeping personas in mind allows
us to use the correct language and make the best decisions to address
their specific problems and pain points.

Our user personas are based on both the organizational structure of
GitLab and the [preexisting GitLab user personas](https://handbook.gitlab.com/handbook/product/personas/#list-of-user-personas),
derived from UX research studies. User personas are people, who use GDK
to contribute to GitLab. They might contribute code directly, review
changes, or test code.

GDK users are split in two broad groups, team members and people from the
broader GitLab community. The latter also includes customers, who
contribute changes to GitLab, either as a dedicated initiative or as part
of the [Co-Create program](https://about.gitlab.com/community/co-create/).

```mermaid
mindmap
  root((GDK users))
    Team members
    Community members
      Individual contributors
      Customers
```

While community contributors outnumber team members, team members
disproportionately spend more time using GDK. This gives us the unique
opportunity to talk to GDK's power users with very little friction. Most
of them are team members, who are a single Slack message or coffee chat
away from sharing their experience using GDK with us.

## Personas

### Mia (ML Engineer)

- [Handbook page](https://handbook.gitlab.com/handbook/product/personas/#mia-ml-engineer)
- Their current use cases often span multiple components which they need to actively
  iterate on in quick succession.   

### Sasha (Software Developer)

- [Handbook page](https://handbook.gitlab.com/handbook/product/personas/#sasha-software-developer)

### Simone (Software Engineer in Test)

- [Handbook page](https://handbook.gitlab.com/handbook/product/personas/#simone-software-engineer-in-test)

### Parker (Product Manager)

- [Handbook page](https://handbook.gitlab.com/handbook/product/personas/#parker-product-manager)

### Presley (Product Designer)

- [Handbook page](https://handbook.gitlab.com/handbook/product/personas/#presley-product-designer)

## Usage matrix

The purpose of development environments can broadly be categorized in two
types: creating and validating (code) changes.

Most GDK users fall in one of the two high-validation quadrants but not
every user needs to change code, especially not at all times.

```mermaid
quadrantChart
    title Change creation and validation
    x-axis Low creation --> High creation
    y-axis Low validation --> High validation
    Engineers: [0.9, 0.75]
    Engineers in Test: [0.62, 0.85]
    Product/Engineering Manager: [0.2, 0.9]
    Product Designer: [0.34, 0.8]
```
