# frozen_string_literal: true

module GDK
  module Command
    # Handles `gdk reset-openbao-data` command execution
    class ResetOpenbaoData < BaseCommand
      help 'Remove OpenBao PostgreSQL data'

      def run(_ = [])
        return false unless continue?

        execute
      end

      private

      def continue?
        GDK::Output.warn("We're about to remove OpenBao PostgreSQL data.")

        return true if ENV.fetch('GDK_RESET_OPENBAO_DATA_CONFIRM', 'false') == 'true' || !GDK::Output.interactive?

        GDK::Output.prompt('Are you sure? [y/N]').match?(/\Ay(?:es)*\z/i)
      end

      def execute
        Runit.stop(quiet: true) &&
          # ensure runit has fully stopped
          sleep(2) &&
          start_necessary_services &&
          # ensure runit has fully started PostgreSQL
          sleep(2) &&
          drop_database &&
          recreate_database
        # OpenBao handles all data migrations
      end

      def start_necessary_services
        Runit.start('postgresql', quiet: true)
      end

      def psql_cmd(command)
        GDK::Postgresql.new.psql_cmd(['-c'] + [command])
      end

      def drop_database
        shellout(psql_cmd("drop database #{GDK.config.openbao.database_name}"))
      end

      def recreate_database
        shellout(psql_cmd("create database #{GDK.config.openbao.database_name}"))
      end

      def shellout(command)
        sh = Shellout.new(command, chdir: GDK.root)
        sh.stream
        sh.success?
      end
    end
  end
end
