gitlab-knowledge-graph-proxy_dir = ${gitlab_development_root}/gitlab-zoekt-indexer

.PHONY: gitlab-knowledge-graph-proxy-setup
ifeq ($(knowledge_graph_enabled),true)
gitlab-knowledge-graph-proxy-setup: gitlab-zoekt-indexer/.git/pull gitlab-zoekt-indexer/bin/gitlab-zoekt
else
gitlab-knowledge-graph-proxy-setup:
	@true
endif

# The following targets will be uncommented in a follow-up MR
#.PHONY: gitlab-knowledge-graph-proxy-update
#ifeq ($(gitlab-knowledge-graph-proxy_enabled),true)
#gitlab-knowledge-graph-proxy-update: gitlab-knowledge-graph-proxy-update-timed
#else
#gitlab-knowledge-graph-proxy-update:
#	@true
#endif
#
#.PHONY: gitlab-knowledge-graph-proxy-run
#gitlab-knowledge-graph-proxy-run: gitlab-knowledge-graph-proxy-indexer/.git/pull gitlab-knowledge-graph-proxy-indexer-clean-bin gitlab-knowledge-graph-proxy-indexer/bin/gitlab-knowledge-graph-proxy-indexer
#
#gitlab-knowledge-graph-proxy-tool-install:
#ifeq ($(tool_version_manager_enabled),true)
#	@echo
#	@echo "${DIVIDER}"
#	@echo "Installing mise tools from ${gitlab_gitlab-knowledge-graph-proxy_dir}/.tool-versions"
#	@echo "${DIVIDER}"
#	$(Q)cd ${gitlab_gitlab-knowledge-graph-proxy_dir} && $(MISE_INSTALL)
#else
#	@true
#endif
#
#gitlab-knowledge-graph-proxy-clean-bin:
#	$(Q)rm -rf gitlab-zoekt-indexer/bin/*

# gitlab-knowledge-graph-proxy-indexer/.git:
# 	$(Q)GIT_REVISION="${gitlab_gitlab-knowledge-graph-proxy_indexer_version}" support/component-git-clone ${git_params} ${gitlab_zoekt_indexer_repo} gitlab-zoekt-indexer

# .PHONY: gitlab-knowledge-graph-proxy-indexer/bin/gitlab-knowledge-graph-proxy
# gitlab-knowledge-graph-proxy-indexer/bin/gitlab-knowledge-graph-proxy: gitlab-knowledge-graph-proxy-tool-install
# 	@echo
# 	@echo "${DIVIDER}"
# 	@echo "Building gitlab-org/gitlab-knowledge-graph-proxy version ${gitlab_gitlab-knowledge-graph-proxy_indexer_version}"
# 	@echo "${DIVIDER}"
# 	$(Q)support/tool-version-manager-exec gitlab-knowledge-graph-proxy-indexer $(MAKE) build-unified ${QQ}

#.PHONY: gitlab-knowledge-graph-proxy-indexer/.git/pull
#gitlab-knowledge-graph-proxy-indexer/.git/pull: gitlab-knowledge-graph-proxy-indexer/.git
#	@echo
#	@echo "${DIVIDER}"
#	@echo "Updating gitlab-org/gitlab-knowledge-graph-proxy-indexer"
#	@echo "${DIVIDER}"
#	$(Q)support/component-git-update gitlab-knowledge-graph-proxy gitlab-knowledge-graph-proxy-indexer "${gitlab_gitlab-knowledge-graph-proxy_indexer_version}" main
