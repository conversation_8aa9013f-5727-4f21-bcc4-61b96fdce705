10/29/25, 4:47 PM pydantic model construction/tool formatting using a lot of CPU time in Duo Workflow Service (#578158) · Issue · gitlab-org/gitlab
CPU Duration throughput opti
i
m
zation of Duo Workflow S…
pydantic model construction/tool formatting using a lot of CPU ti
me in Duo
Workflow Service
Open Issue created 6 days ago by <PERSON>
Read more at gitlab-org/quality/quality-engineering/team
-tasks#3794 (comment 2838923664) .
This is a significant factor in throughput of Duo Workflow Service. What is it doing? Can it be opti
m
Can we cache certain things related to schema construction/validation?
i
zed?
Edited 5 days ago by 🤖 G
itLab Bot 🤖
No child items are currently assigned. Use child items to break
down work
into smaller parts.
Link
items together to show that they're related or that one is block
ing others.
Activity
🤖 G
itLab Bot 🤖mentioned in issue gitlab-org/quality/triage-reports#
267382
days ago
Dhruv <PERSON>hi assigned to @rathid 4 days ago
Dhruv Rathi unassigned @rathid 5 days ago
🤖 G
itLab Bot 🤖 changed the description 5 days ago ·
Dhruv <PERSON>hi assigned to @rathid 5 days ago
🤖 G
6 days ago
itLab Bot 🤖 added labels
Category:Duo Agent Platform devops ai
-powered section ai
🤖 G
itLab Bot 🤖 added typebug
label 6 days ago
🤖 G
itLab Bot 🤖 changed the description 6 days ago ·
Dylan Griffith added labels 6 days agobug performance group agent foundations
Dylan Griffith changed the description 6 days ago ·
🤖 G
itLab Bot 🤖 changed the description 6 days ago ·
Dylan Griffith added gitlab-org#
1
9747 as parent epic 6 days ago
Status
New
Assignees
Dhruv Rathi
Labels
Category:Duo Agent Platform
bug performance devops ai
-powered
group agent foundations section ai
typebug
Parent
CPU Duration throughput opti
m
i
zation …
Weight
None
Milestone
None
Iteration
None
Dates
Start: None
Due: None
Health status
None
Subscription
None
Ti
me track
Add an or .
ing
3 Participants
Dylan Griffith set status to New 6 days ago
https://gitlab.com/gitlab-org/gitlab/-/issues/578158 1/1



10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
Infrastructure Platforms - Developer Experience Load test agentic chat and SW development flows
Open Confidential
Agentic AI performance test execution and data gathering
Agentic AI performance test execution and data gathering
Open Confidential
Issue created 3 months ago by Brittany Wilkerson
Summary
Collaboration issue for Performance Enablement to hand off instructions, scripts and the environment built in previous issues, and provide mentorship to
the Duo Workflow team to help them execute a stress test and gather data.
Tasks
1. Attempt to reproduce maxi
mum sustained Production load (Data shows max of 1
0 RPS, w
RPS
ith upper-normal range consistently reaching
between 7-8
)
1. Collect performance data when sustaining
1
0 RPS for 60 seconds (using Agentic Chat to summari
ze an issue, which w
executor)
2. Collect via dws-loadtest (prod) using real LLM calls
3. Collect via dws-loadtest.staging using mocked LLM calls
4. Compare performance of DWS and of G
itLab
monolith w
ith real vs. mocked LLM calls
2. Load test
1
0x max Production load - target 80 RPS sustained for 5 m
ins,
00 RPS
1
max
3. Use api
_v4_duo_workflow_chat_graphql_api.js to test Agentic Chat (w
ith mocked LLM calls) as if via the UI
4. Use api
_v4_duo_workflow_chat_hybrid_api.js to test Flows triggered via the API that use the CI executor (w
ith mocked LLM calls) - si
m
triggering Flows via the UI
5. Analyze performance and consider if real vs. mocked LLM calls would affect conclusions
ill use Workhorse as
ilar to
Notes
Edited
Some Grafana dashboards of note:
https://dashboards.gitlab.net/goto/Mevf6uUHR?orgId=
1
https://dashboards.gitlab.net/d/deh7rohkbadxcc/duo-workflow-performance-
metrics?from=now-2
d&orgId=
&ti
mezone=utc&to=now
1
We should also review
Database and G
G
it/
italy performance as part of this, due to the roles they play in checkpointing
1
week ago by Mark Lapierre
Status
New
Assignees
Mark Lapierre
Labels
devops ai
-powered group ai framework section data-scienceworkflow
in dev
Parent
Load test agentic chat and SW development flows
Weight
None
Milestone
None
Iteration
None
Dates
Start: None
Due: None
Health status
None
Subscription
None
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 1/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
Ti
me track
ing
Add an or .
Open Confidential
Agentic AI performance test execution and data gathering
1
5 Participants
No child items are currently assigned. Use child items to break
down work
into smaller parts.
Related to
professional_teal_sparrow - Duo Agent requests failing w
gitlab-com
/gl-
infra/gitlab-dedicated/incident-
management#
1734
IncidentActive group environment automation
ith ti
meouts Open
Investigate if a single CPU is better for autoscaling in Cloud Run for Duo Workflow Service Complete Closed
gitlab-org/gitlab
#578
54
1
Category:Duo Agent Platform automation:m
lbug performance devops ai
-powered group agent foundations section ai typebug
fix: set 4G
mem
li
m
it for dws-loadtest Runway service and 45 max concurrency ai
-assist!3684
8.6 Merged
1
chore: provide vertex location for DWS loadtest env ai
-assist!3586
8.6 Merged
1
chore: enable mock responses on dws loadtest staging ai
-assist!3575
8.6 Merged
1
fix: allow failure in dws-loadtest jobs to unblock pipelines ai
-assist!3504
8.5 Merged
1
chore: Add m
lapierre to dws-loadtest provisioner!983 Merged
test: add agentic chat load tests ai
-assist!36
5
1
chore: configure DWS runway w
ith less CPU and memory and ai
-assist!3700
8.6 Closed
1
Add a new workflow test for workflow completion performance!658 Closed
Activity
Mark Lapierre
1
day ago Maintainer
A quick update based on gitlab-org/gitlab
#578
1
54 (comment 2848603869)
With the recent code changes, and w
another test for
0
1
inutes - all requests succeeded). 🚀
ith Google profiler disabled, a single container was able to sustain
1
99 concurrent connections (I ran
m
That's much i
mproved over the previous best of only around 45 concurrent connections before losing requests.
Next: Investigate and try to reproduce high memory usage (> 50%)
Mark Lapierre 1
day ago Internal note Maintainer
Production memory usage someti
mes exceeds 50%: gitlab-org/
modelops/applied-
l/code-suggestions/ai
-assist!3700 (comment
2846029376)
m
During load testing so far it hasn't exceeded about 20%. It's possi
ble mock
ing LLM calls keeps memory usage down. It's also possi
ble high
memory usage is linked to large contexts.
More investigation is needed.
Mark Lapierrementioned in merge request gitlab-org/
modelops/applied-
l/code-suggestions/ai
-assist!3684 (merged) 5 days ago
Mark Lapierrementioned in issue gitlab-org/gitlab
#578
1
67 (closed) 6 days ago
G
itLab
6 days ago
Infrastructure Service -
incident.iomarked this issue as related to gitlab-com
/gl-
infra/gitlab-dedicated/incident-
management#
1734
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 2/23
m
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
G
itLab
Infrastructure Service -
incident.iomentioned in incident gitlab-com
Open Confidential
Agentic AI performance test execution and data gathering
Dylan Griffithmentioned in issue gitlab-org/gitlab
#578
1
64 (closed) 6 days ago
/gl-
infra/gitlab-dedicated/incident-
management#
1734 6 days ago
Dylan Griffithmentioned in issue gitlab-org/gitlab
#578
1
53 (closed) 6 days ago
Dylan Griffithmentioned in issue gitlab-org/gitlab
#578
1
54 (closed) 6 days ago
Dylan Griffithmarked this issue as related to gitlab-org/gitlab
#578
1
54 (closed) 6 days ago
Dylan Griffithmentioned in issue gitlab-org/gitlab
#578
1
55 (closed) 6 days ago
Dylan Griffithmentioned in issue gitlab-org/gitlab
#578
1
56 6 days ago
Dylan Griffithmentioned in issue gitlab-org/gitlab
#578
1
57 6 days ago
Dylan Griffithmentioned in issue gitlab-org/gitlab
#578
1
58 6 days ago
Dylan Griffithmentioned in issue gitlab-org/gitlab
#578
1
59 (closed) 6 days ago
Dylan Griffithmentioned in issue gitlab-org/gitlab
#578
1
60 (closed) 6 days ago
Dylan Griffithmentioned in epic gitlab-org#
1
9747 6 days ago
Dylan Griffithmentioned in issue gitlab-org/
modelops/applied-
m
l/code-suggestions/ai
-assist#
1
583 6 days ago
Mark Lapierre 6 days ago Compare performance of DWS and of G
itLab
monolith w
ith real vs. mocked LLM calls
We can compare performance profiles for the production DWS
vs. the loadtest DWS
(w
ith mocked LLM calls):
Wall ti
me:
Maintainer
Prod Loadtest
CPU ti
me:
Prod Loadtest
Wall vs CPU ti
me ratio is si
m
ilar, but the CPU profiles are quite different.
Edited 6 days ago by Mark Lapierre
Mark Lapierre
1
week ago Maintainer
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 3/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
Attempt to reproduce maxi
mum sustained Production load (Data shows max of 1
0 RPS, w
ith upper-normal range consistently reaching
between 7-8 RPS
)
Open Confidential
Agentic AI performance test execution and data gathering
1. Collect performance data when sustaining
1
0 RPS for 60 seconds (using Agentic Chat to summari
ze an issue, which w
ill use
Workhorse as executor)
2. Collect via dws-loadtest (prod) using real LLM calls
To collect that data, it would help if we had a Production license we can use w
ith the test instance.
@poffey2
1
Would you be able to provide a Prem
suggestions/ai
-assist#
1
579, but I think we w
ium production license? This is unrelated to gitlab-org/
modelops/applied-
ill only need one license and just use it in both test environments.
cc @wortschi
m
l/code-
Mark Lapierre changed the description
1
week ago ·
Mark Lapierre
1
week ago Maintainer
I ran some tests w
ith the DWS configured to return mocked responses for agentic chat flows:
a test that starts a flow
a test that starts a flow
via graphql, li
ke the UI does (so workhorse proxies requests to Rails. No other executor)
via the RES
T API
w
ith remote execution
Looks li
ke both the DWS and G
itLab have no trouble running up to about 50 RPS
in either case.
On Monday I'll run some more tests and share some data
cc @wortschi
Martin Wortschack
1
week ago Developer
@
lapierre Great work and prom
ising results so far! Thanks for the update! Please keep us posted here
Mark Lapierre
1
week ago Maintainer
Looks li
ke both the DWS and G
itLab have no trouble running up to about 50 RPS
in either case.
I
was wrong about that. Very wrong.
I m
isunderstood how the performance test tool was configured. Essentially, I thought it would try to execute a given num
per second, for a given duration, regardless of how long each request takes. It's that last b
it that I m
ber of requests
isunderstood. Turns out it waited
until a request finished
before starting another one.
So I had to figure out how to make it actually execute a given num
was able to generate that many requests.
ber of requests per second. And then how to modify my machine so it
When I
was able to run the tests properly it generated a bunch of errors. And then the grafana instance stopped responding, so I don't
know
if the errors are due to overwhel
ing the test environment.
m
But at least the DWS service seemed to cope w
ith close to 20 RPS for 20 seconds:
m
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 4/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
Open Confidential
Agentic AI performance test execution and data gathering
More data tomorrow once I get access to the data again.
cc @wortschi FY
I
Mark Lapierre
1
week ago Maintainer
Yeah, looks li
ke grafana couldn't handle the load. Not very helpful...
Martin Wortschack
@
lapierre
1
week ago Developer
When I
was able to run the tests properly it generated a bunch of errors. And then the grafana instance stopped responding, so I
don't know
if the errors are due to overwhel
ing the test environment.
m
Were these errors logged at all?
Brittany Wilkerson
1
week agoAuthor Developer
@
lapierre We can increase the specs on the monitor node if you need.
It'd
be an update to this line: https://gitlab.com
test-agentic-chat/configs/load-test-agentic-chat/terraform
even a n1-highcpu-8 .
/gitlab-org/quality/gitlab-environment-toolk
-
it-configs/performance-test-rfh/
/
blob
/load-
/environment.tf?ref_type=heads#L3
1
We can bump it to a n1-highcpu-4 or
Then you'd need to rerun the deploy+configure. You should
monitor node, but you m
ight lose previous run data?
be able to do it w
ithout tearing it down first I think, it should replace the
The other consideration would
upgrade to the latest Nightly.
be that it's configured to use the Nightly builds, so the G
itLab
instance would al
most certainly also
Mark Lapierre
1
week ago Maintainer
@wortschi
I'm able to reproduce the errors consistently. The test shows some requests fail to complete successfully:
m
m
✗ received workflow messages via websocket
↳ 49% — ✓ 117 / ✗ 119
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 5/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
✗ workflow completed successfully
↳ 49% — ✓ 117 / ✗ 119
Open Confidential
The logs look li
Agentic AI performance test execution and data gathering
ke what I saw from yesterday. I see errors li
ke:
/var/log/gitlab/gitlab-workhorse/current:{
"correlation_id": "01K84DJZPR8EAAG2F2RAFN6PK8",
"error": "d
uoworkflow: failed to read a gRPC message: rpc error: code = Internal desc = stream terminate
"level": "error",
"method": "GET",
"msg": "",
"time": "2025-10-21T22:18:29Z",
"uri": "/api/v4/ai/d
uo_workflows/ws?project_id=1000000\u0026root_namespace
_id=1000000"
}
{
/var/log/gitlab/gitlab-workhorse/current:
"correlation_id": "01K84DJY098VYE55161JBY5SG6",
"error": "handle
gentAction: failed to send gRPC message: EOF",
"level": "error",
"method": "GET",
"msg": "",
"time": "2025-10-21T22:18:32Z",
"uri": "/api/v4/ai/d
uo_workflows/ws?project_id=1000000\u0026root_namespace
A
_id=1000000"
}
{
/var/log/gitlab/gitlab-workhorse/current:
"correlation_id": "01K84EAS523B7VHMX72GSSS3B6",
"error": "handleWebSocketM
essage: failed to read a WS message: websocket: close 1000 (normal)",
"level": "error",
"method": "GET",
"msg": "",
"time": "2025-10-21T22:31:45Z",
"uri": "/api/v4/ai/d
uo_workflows/ws?project_id=1000000\u0026root_namespace
_id=1000000",
}
{
/var/log/gitlab/gitlab-workhorse/current:
"correlation_id": "01K84EAS523B7VHMX72GSSS3B6",
"d
uration_ms": 332,
"error": "badgateway: failed to receive response: context canceled",
"level": "error",
"method": "POST",
"msg": "",
"time": "2025-10-21T22:31:45Z",
"uri": ""
}
Essentially all errors suggesting that the websocket closed prematurely. But I don't see anything in the associated logs that would
explain why that happened. Logs li
ke that don't appear when the test runs successfully.
Mark Lapierre
1
week ago Maintainer
@bw
ilkerson
1
3 Thanks, I'll try that upgrade
Mark Lapierre
1
week ago Maintainer
When attempting to sustain 20 RPS for 20 seconds (w
ith a very si
mple chat conversation that si
mulates 3 responses that take 2-5
seconds each), Postgres shows the highest CPU usage, followed
by Rails. But none are at
00%:
1
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 6/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
Open Confidential
Agentic AI performance test execution and data gathering
Li
kew
ise for the DWS:
Edited
1
week ago by Mark Lapierre
Mark Lapierre
1
week ago Maintainer
@bw
ilkerson
1
3 Is there any option for view
ing logs other than to ssh into the nodes and read the log files?
Mark Lapierre
1
week ago Maintainer
Digging into the errors...
Test run attempting
1
5 rps for 20 seconds
During the test, 30
1
total iterations (in each iteration it sends a chat request and then opens a websocket connection to recieve the
responses for that request)
28
1
iterations succeeded
20 iterations failed - no responses received
DWS logs show:
30
1
requests to https://dws-loadtest.staging.runway.gitlab.net/DuoWorkflow/ExecuteWorkflow
only 28
1
entries for the event request_
d
uo_workflow - so it looks li
ke whatever prevented the other 20 requests happened
before the workflow started
45 runti
me errors - not sure how
/if they're related to the m
issing requests
3 Y
AML errors?
a langgraph checkpointer error
92 errors logged
by asyncio Task was destroyed but it is pending!
If I run the same load test at a reduced rate of 5-
1
0 rps, I get
0
1
1
☝
successful requests w
ith responses and none of those errors
Edited
1
week ago by Mark Lapierre
Mark Lapierre
1
week ago Maintainer
@bastirehm FY
I, I asked in Slack, but it's starting to look li
ke maybe the DWS
isn't able to handle
1
0+ RPS. I'll keep look
ing into it
tomorrow, but if anyone in your team could take a look too that would
be great.
cc @wortschi
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 7/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
Mark Lapierre
1
week ago Maintainer
It's worth noting that the max rps seen in prod recently is
Open Confidential
1
3.6 rps and scaling is set to tw
ice the loadtest environment (prod vs loadtest).
Agentic AI performance test execution and data gathering
So if load really is a problem, it's possi
ble we'll start to see si
m
ilar problems in prod when we reach somewhere around 20-30 rps.
Martin Wortschack
1
week ago Developer
@
lapierre Thank for the update here.
Essentially all errors suggesting that the websocket closed prematurely.
I
wonder if this related to what has been reported in Delta A
irlines Hackathon - Duo Failure RCA (gitlab-org/gitlab
had reportedly 250+ concurrent users and agentic chat became unresponsive. We were not able to reproduce this problem
on staging where we ran an evaluation pipeline for Duo VR to generate load and then tried to manually interact w
#570203) where we
back then
ith agentic chat.
Let me see if we have any data on RPS for the problem reported during the incident.
Edit: The above mentioned issue may not be directly related to high load as we didn't exceed 6 RPS
in this case.
Edited 6 days ago by Martin Wortschack
Brittany Wilkerson
1
week agoAuthor Developer
@bw
ilkerson
1
3 Is there any option for view
ing logs other than to ssh into the nodes and read the log files?
@
lapierre
Not in a GET setup right now, no.
I have an Issue sketched out somewhere to create a custom task to add Lok
i (the Grafana Labs log aggregator/viewer, so you'd
to view them
in Grafana next to the metrics) to the monitoring stack, but it's a little b
it of a project.
be able
Are you still going to be testing w
ith this environment for a little b
it (li
ke a week or two? Longer?)? I don't m
it, but if you're already wrapping up, it won't make a ton of sense if you're done by the ti
me that I am 😅
ind jumping in to try and add
Edited 6 days ago by Brittany Wilkerson
Sebastian Rehm 6 days ago @halilcoban , @DylanGriffith , @igor.drozdov fyi on the potential load problems discovered w
Developer
ith Marks load tests
Dylan Griffith 6 days ago Developer
@
lapierre I should
be available to help investigate this a little today. Right now
I see you have collected workhorse logs that suggest
we someti
mes drop connections. This is a little tricky to diagnose from those alone. You also mentioned that Postgres had high CPU at
one point. I'm
wondering if the high CPU was consistent and correlates w
ith disconnects? A
lso some other data points that would
be
interesting would
be:
m
1. Postgres slow logs. Are there slow
queries?
2. What does our client think
is happening throughout all of this? Is it reporting errors? Is it closing connections?
3. What k
ind of load
balancers are involved here? Do the websocket requests get routed via any load
balancer that may have ti
meouts
configured?
4. What specific flows are running. You mention Y
AML errors? Are these custom flows configured via Y
AML?
5. What is the single CPU utili
zation of DWS. The python code runs single threaded so total CPU utili
zation of the host may be
isleading. In asyncio python code it's easy to block up the entire server by having any slow CPU processes. If Y
AML parsing
regularly appears in logs we m
ight want to collect a flamegraph of how
much ti
me is spent parsing Y
AML. Maybe that's very
expensive for some reason?
6. Related to ☝ what are we seeing for cpu_s on our Python server. How
does that cpu_s compare w
ith d
uration_s . For example
if our logs say cpu_s=0.5 and d
uration_s=1 then this means that a single flow
is consum
ing CPU half the ti
me. If that is the case
we can logically only sustain 2 RPS. Our assumption was that we should
be al
most entirely I
O bound (waiting for HTTP/gRPC
requests mostly) and therefore CPU usage should
be very m
ini
mal and thus we could run hundreds of concurrent requests.
m
These are just some avenues to explore.
If we find single CPU saturation then we'll want to collect some flame graphs of the Python process to assess where the CPU ti
me is
spent and figure out how we can opti
m
i
ze that.
Mark Lapierre 6 days ago Maintainer
Are you still going to be testing w
ith this environment for a little b
it (li
ke a week or two? Longer?)?
@bw
ilkerson
1
3 Yeah, probably at least a week for this round of testing, then more later to help load test the security analyst agent.
m
m
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 8/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
Mark Lapierre 6 days ago @DylanGriffith Thanks, that's hugely helpful!
Open Confidential
Agentic AI performance test execution and data gathering
Maintainer
1. Postgres slow logs. Are there slow
queries?
The logs do show a few records li
ke:
2025-10-21_23:53:07.92609 LOG: d
uration: 1403.032 ms bind
<unnamed
>: /*application:web,correlation_id:01
But is
.4s slow
?
1
2. What does our client think
is happening throughout all of this? Is it reporting errors? Is it closing connections?
It's a k6 script. It uses GraphQL to start a workflow, then opens a websocket and passes the test if it receives at least
1 message. It
reports a ti
meout if there are no messages and the websocket isn't closed
by the server after 80 seconds.
Over several test runs, someti
mes it doesn't report any errors, just m
and the websocket wasn't closed
by the server.
issing messages, and someti
mes it reports ti
meouts - no message
It hasn't caught any websocket errors.
3. What k
ind of load
ti
meouts configured?
balancers are involved here? Do the websocket requests get routed via any load
balancer that may have
The test environment is based on the 3k reference architecture. I don't actually know
much about the configuration of the load
balancers.
@bw
ilkerson
1
3 Do you know how the ti
meouts are configured?
4. What specific flows are running. You mention Y
AML errors? Are these custom flows configured via Y
AML?
It's just agentic chat. But it's using mock
ing to avoid real LLM calls.
5. What is the single CPU utili
zation of DWS.
Is that something I can get from Cloud Run or Runway logs/
metrics? It's the cpu utili
zation in the dashboard
dws-loadtest staging Runway service. I don't see single-
what are we seeing for cpu_s on our Python server. How
does that cpu_s compare w
ith d
uration_s
During that test run I reported yesterday ☝ , the averages for the Finished ExecuteWorkflow RPC event).
cpu_s and d
uration_s were
1
5 and 50 (taken from the log entries w
ith
FY
2s.
1
I The LLM mock
ing is designed to si
mulate delayed responses, and the test was designed to delay responses for a total of around
we'll want to collect some flame graphs of the Python process
I
was curious about this especially to compare mocked LLM calls w
conclusions about performance.
ith real LLM calls, to be confident mock
ing doesn't lead to invalid
But I haven't done any Python profiling yet. Do we have any guidance on that?
Mark Lapierre 6 days ago Ah, profiling is enabled in GCP
Dylan Griffith 6 days ago Maintainer
Developer
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 9/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
Open Confidential
Agentic AI performance test execution and data gathering
source
Profiler findings:
1. Our Cloud Connector li
brary is mak
ing HTTP requests w
ithout using asyncio HTTP li
braries https://gitlab.com
/gitlab-org/cloud-
connector/gitlab-cloud-
-
connector/
/
blob
/46a6f6dab50a0f6775d971
2bc6e20eddfa7095
1
3/src/python/gitlab_cloud_connector/providers.py#L34
. This
1
blocks reactor. This is a substantial amount of CPU ti
me and reducing concurrency.
2. CPU autoscaling m
ight not work
if we run single threaded and the container has 2 cpus. So we can never exceed 50% . Maybe we
should request
CPU
1
3. This code is also using a lot of CPU ti
me https://gitlab.com
/gitlab-org/cloud-connector/gitlab-cloud-
-
connector/
/
blob
/46a6f6dab50a0f6775d971
2bc6e20eddfa7095
1
3/src/python/gitlab_cloud_connector/providers.py#L2
77 . What is
it doing? Is it actually CPU or block
ing I
O?
4. Why are we doing JWK
S stuff on every request? Surely we just load
keys on startup and then verify them quickly for each request
using cached
keys? Maybe tricky due to how our auth works but we can surely cache things. At a m
ini
mum
we should
be caching
keys for gitlab.com even if some self-
managed instances have different keys.
5. Snowplow tracker is also mak
ing
block
ing HTTP requests
6. prometheus_app is also using a lot of CPU ti
me what is it doing?
7. Why is pydantic model construction/tool formatting using so much ti
me?
8. Quite a lot of ti
me spent json encoding. The largest chunk seems to be our checkpoint notifier which is what streams the UI updates
to chat w
indow. We probably can opti
i
m
ze this as we are sending more frequently than we need.
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 10/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
Open Confidential
Agentic AI performance test execution and data gathering
9. Sentry transaction profiler seems to be using 5% of our CPU ti
me. We should probably disable this
Edited 6 days ago by Dylan Griffith
Dylan Griffith 6 days ago There is a lot of low hanging fruit there for reducing CPU ti
me and increasing throughput.
Developer
Dylan Griffith 6 days ago @bastirehm
based on the findings I've extracted gitlab-org&
1
9747 . There's a lot of low hanging fruit in there which should help w
Developer
ith
throughput. The first thing we should tackle is gitlab-org/gitlab
#578
1
54 (closed) because at least if we can get autoscaling to work well
then the CPU block
ing is more of a cost issue rather than a hard scaling li
m
it.
Mark Lapierre 6 days ago Maintainer
it's starting to look li
ke maybe the DWS
isn't able to handle
0+ RPS.
1
@bastirehm
@wortschi @DylanGriffith I think
I have some reassuring news. It's actually not quite that bad. 😅
The DWS can handle
1
0+ RPS, as long as that load isn't sustained. The problems occur when the num
ber of concurrent connections
gets too high. That happens quickly when trying to sustain
1
5 RPS for 20 seconds where each request takes 20+ seconds to complete
(i.e., 300 concurrent connections in the last second, if it managed to get that far).
The test I reported yesterday got up to around 85 concurrent requests (across 4 instances):
When I more gradually increase the load, I
was able to get up to around 50 concurrent requests on a single instance before I started to
see failures:
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 11/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
Open Confidential
Agentic AI performance test execution and data gathering
So we should have some room to scale up the rate of requests. At least as long as we don't need to sustain high RPS for long.
Edited 6 days ago by Mark Lapierre
Mark Lapierre 6 days ago I'll test a lower max concurrency setting after testing
Maintainer
CPU.
1
One or both changes w
ill hopefully let autoscaling work effectively and avoid losing requests.
Edited 6 days ago by Mark Lapierre
Brittany Wilkerson 6 days agoAuthor Developer
The test environment is based on the 3k reference architecture. I don't actually know
much about the configuration of the load
balancers.
@bw
ilkerson
1
3 Do you know how the ti
meouts are configured?
@
lapierre
There's an internal and an external load
balancer, both are using HAProxy.
The two nodes are defined in Terraform.
They're configured in GET via this role.
The default config templates for them are here.
There's also an override task
in this specific environment, for the external load
remove default Rails rate li
m
iting.
balancer, to enable the /-/grafana route and to
The base haproxy.cfg template in GET defines the follow
ing ti
meouts:
m
timeout connect 30s
timeout client 305s
timeout server 305s
Those would
be generic to both internal and external. During the Ansi
ble setup, it'll take that generic haproxy.cfg.j2 and com
b
w
ith the contents of haproxy_internal.cfg.j2 or haproxy_
external.cfg.j2 to make a single file for the type of node that it is.
ine it
For external specifically, then the custom task runs and makes an edit to the generated li
m
iting exclusion that was mentioned above.
haproxy.cfg to add the Grafana route + rate
There's no additional ti
meout overrides in the sub-templates or the custom tasks.
We can figure out a custom task addition if we need to adjust the ti
meouts for this environment.
Brittany Wilkerson 6 days agoAuthor Developer
Are you still going to be testing w
ith this environment for a little b
it (li
ke a week or two? Longer?)?
@bw
ilkerson
1
3 Yeah, probably at least a week for this round of testing, then more later to help load test the security analyst agent.
Okay, let me see what I can do.
Reply…
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 12/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
Mark Lapierrementioned in merge request gitlab-org/
modelops/applied-
l/code-suggestions/ai
-assist!36
5
1
1
week ago
m
Open Confidential
Mark Lapierre
Agentic AI performance test execution and data gathering
1
week ago Maintainer
Adding a new test that waits for workflows to finish: gitlab-org/quality/performance!658 (closed)
Martin Wortschack
1
week ago Developer
@
lapierre Is this test perform
ing LLM requests or mock
ing those requests?
Mark Lapierre
1
week ago Maintainer
@wortschi The test expects the DWS to be configured to mock requests. I'll add a comment to the test about that.
m
Mark Lapierre
1
week ago Maintainer
Adding the test in the AI
GW repo, li
ke the other ones. gitlab-org/
modelops/applied-
l/code-suggestions/ai
-assist!36
5
1
m
Reply…
Mark Lapierrementioned in merge request gitlab-org/quality/performance!658 (closed)1
week ago
Sebastian Rehmmade the issue confidential2 weeks ago
Sebastian Rehmmade the issue visi
ble to everyone2 weeks ago
Sebastian Rehmmade the issue confidential2 weeks ago
Mark Lapierrementioned in merge request gitlab-org/
modelops/applied-
m
l/code-suggestions/ai
-assist!3586 (merged)2 weeks ago
Mark Lapierre2 weeks ago Maintainer
It would
be useful to be able to compare the load test results to existing load. Based on the GCP data for the last 2 weeks, it looks li
ke there
was one spi
ke of about 30 concurrent requests, but usually there are only about 2 to 5. That seems very low 🤔
In grafana it looks li
ke it's usually up to about 6 or 7 RPS
orgId=
1
during the week
(max
1
0): https://dashboards.gitlab.net/goto/
bf0wcgracr3swc?
Martin Wortschack 2 weeks ago @bastirehm Do you have current RPS
used
by internal teams daily.🤔
Developer
data for DWS
? Mark's num
bers seem
lower than I'd expect considering DWS
is actively being
Sebastian Rehm
2 weeks ago Developer
@wortschi the data should actually be correct, you have to keep in m
ind that we currently have around ~3000 sessions per day only.
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 13/23
10/29/25, 4:48 PM cc @halilcoban to confirm that there's no problem
Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
w
ith the metrics I m
ight have m
issed.
PS: I also made this issue confidential since we're k
ind of sharing usage of a beta product here.
Open Confidential
Agentic AI performance test execution and data gathering
Reply…
Mark Lapierre2 weeks ago Maintainer
Need to enable mock
ing on the dws loadtest server: gitlab-org/
modelops/applied-
l/code-suggestions/ai
-assist!3575 (merged)
m
Mark Lapierre2 weeks ago Maintainer
It's enabled, but there's another problem:
{
"level": "error",
"ts": 1760401828.828781,
"caller": "d
uo-workflow-executor/main.go:129",
"msg": "Failed to run",
"correlation_id": "01K7G1X7CT1MN7B11H54E98047",
"workflow_id": "19",
"error": "failed to execute runner: error receiving event: rpc error: code = Unknown desc = Unexpected
<
"stacktrace": "main.main\n\t/builds/gitlab-org/d
uo-workflow/d
uo-workflow-executor/main.go:129\nruntime.m
}
Fix in gitlab-org/
modelops/applied-
l/code-suggestions/ai
-assist!3586 (merged)
m
Mark Lapierre2 weeks ago Maintainer
New error:
{
"insertId": "68eec72a000eb2664b95480c",
"jsonPayload": {
"exception_class": "PermissionDeniedError",
"timestamp": "2025-10-14T21:56:58.686878Z",
"level": "error",
"gitlab_
global_user_id": "undefined",
"status_code": null,
"logger": "exceptions",
"exception": "Traceback (most recent call last):\n File
\"/home/<USER>/app/d
uo_workflow_service/ll
"event": "Error code: 403 - {'
error': {'code
': 403, 'message
': \"Permission 'aiplatform.endpoints.pred
"workflow_id": "undefined",
"additional_
details": {},
"correlation_id": "undefined"
},
"resource": {
"type": "cloud
_run_revision",
"labels": {
"location": "us-east1",
"service
_name": "dws-loadtest",
"project_id": "gitlab-runway-staging",
"configuration_name": "dws-loadtest",
"revision_name": "dws-loadtest-fexc"
}
},
"timestamp": "2025-10-14T21:56:58.963174Z",
"labels": {
"container_name": "ingress",
"instanceId": "0069c7a9885a95f116e61df37b7ed1cf20a7693a95e6042fd79033871fcd347f373e519d892743fd122e4ff
},
"logName": "projects/gitlab-runway-staging/logs/run.googleapis.com%2Fstderr",
"receiveTimestamp": "2025-10-14T21:56:59.140778201Z",
"errorGroups": [
{
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 14/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
"id": "CLax_
dauqP6LoAE"
}
]
Open Confidential
}
Agentic AI performance test execution and data gathering
Mark Lapierre2 weeks ago Maintainer
This probably happens in validate
_llm_access . It's not necessary while we're mock
ing responses.
Options:
1. Figure out how to give dws-loadtest access to Sonnet 4 on gitlab-ai-framework-stage
2. Unset A
IGW_GOOGLE_CLOUD
_PLATFORM__PROJECT so that it uses Anthropic servers instead of Vertex
3. Bypass validate
_llm_access when A
IGW_MOCK_MODEL_RESPONSES=true
4. Something else?
Options 2 and 3 would require a code change, although option 2
w
ith real responses is comparable to performance w
is trivial. Option
(or 2
1
ith mocked responses.
) would
be necessary to confirm performance
@wortschi @bastirehm Do you know what would
be necessary to give the dws-loadtest gitlab-ai-framework-stage ? Me too, since I don't have access to that GCP project (but I do have access to prod )?
Runway service access to Sonnet 4 on
gitlab-ai-framework-
Martin Wortschack 2 weeks ago @nateweinshenker
Developer
Do you know what would
stage ?
be necessary to give the dws-loadtest Runway service access to Sonnet 4 on gitlab-ai-framework-
Would you know the answer to this? Didn't you run into si
m
gitlab-ai-framework-prod ?
ilar issues lately when m
igrating agentic features to Anthropic-on-Vertex for
@
lapierre As for access to gitlab-ai-framework-stage -
most (if not all) team mem
gitlab-com
/gl-security/corp/issue-tracker#
267
2 a while ago - w
ill follow up there.
m
bers lack access to this project. I opened a AR in
Martin Wortschack 2 weeks ago looping in @alejandro as well in case you know how to give our Developer
dws-loadtest Runway service the required model access
A
lejandro Rodríguez
@wortschi @
m
1
week ago Developer
lapierre looks li
ke we got that sorted out (thanks to @reprazent 🙂 )
As for the correct code change, I think
endpoint that does that instead.
it would
be to not run validate
_llm_access on server startup but to have a liveness gRPC
lejandro Rodríguez
1
week ago Developer
looks li
ke we got that sorted out
Sorry, I m
isunderstood. The problem
is not Sonnet 4 being enabled in gitlab-ai-framework-stage , but it being accessi
ble from
dws-
loadtest . I assume it must be equivalent to how we enabled access from
gitlab-runway-staging to gitlab-ai-framework-stage ,
but look
ing at additional_roles in Runway's inventory files there's nothing related to Vertex 🤔 . @gsgl am I
look
ing at the wrong
place?
A
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 15/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
Gonzalo Servat
1
week ago Developer
@alejandro that's done in config-
mgmt:
Open Confidential
Agentic AI performance test execution and data gathering
https://ops.gitlab.net/gitlab-com
/gl-
infra/config-
-
mgmt/
/
blob
/
main/environments/ai
-framework-stage/service_accounts.tf
You'll need something li
ke:
resource "google
_project_iam_member" "gitlab-dws-loadtest-staging-vertex-ai" {
project = var.project
role = "roles/aiplatform.user"
member = "service
Account:<EMAIL>"
}
Edited
1
week ago by Gonzalo Servat
Martin Wortschack
1
week ago Developer
@
lapierre Can you please add this to the config-
mgmt and report back
if this is solving the issue you ran into? ☝
m
Martin Wortschack
1
week ago Developer
I opened a MR in https://ops.gitlab.net/gitlab-com
/gl-
infra/config-
-
mgmt/
/
merge_requests/
2392
1
Mark Lapierre
1
week ago Maintainer
@wortschi
It's work
ing! Running tests now...
Mark Lapierre
@gsgl Would you be able to create an MR si
m
but for Prod?
1
week ago Maintainer
ilar to https://ops.gitlab.net/gitlab-com
/gl-
infra/config-
-
mgmt/
/
merge_requests/
2395,
1
resource "google
_project_iam_member" "gitlab-dws-loadtest-prod
uction-vertex-ai" {
project = var.project
role = "roles/aiplatform.user"
member = "service
Account:crun-dws-loadtest@gitlab-runway-prod
uction.iam.gserviceaccount.com"
}
cc @wortschi
Gonzalo Servat
1
week ago Developer
@
m
lapierre : sure... filed https://ops.gitlab.net/gitlab-com
/gl-
infra/config-
-
mgmt/
/
merge_requests/
2
1
4
4... just getting it reviewed &
1
applied.
Gonzalo Servat
1
week ago Developer
@
lapierre : done
m
Mark Lapierre 6 days ago @gsgl Sorry to keep bugging you, but is there any chance you could delete the dws-loadtest prod service? It's show
Maintainer
ing 0 traffic after
deployment. I guess whatever went wrong w
ith the initial staging deployment also affected prod, and was hopefully fixed in the mean
ti
me.
Reply…
Mark Lapierrementioned in merge request gitlab-org/
modelops/applied-
m
l/code-suggestions/ai
-assist!3575 (merged)2 weeks ago
Mark Lapierre2 weeks ago Maintainer
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 16/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
Something weird is going on:
Open Confidential
curl -H "Private-Token: $GITLAB_TOKEN" \
Agentic AI performance test execution and data gathering
-H "Content-Type: application/json" \
-X POST \
-d
'{
"project_id": 1000000,
"start_workflow": true,
"goal": "Open a new MR in the project with a file called hello.rb which is a simple Ruby hello world",
"workflow_
definition": "software
_
development",
"pre
_approved
_agent_privileges": [1, 2, 3, 4, 5],
"agent_privileges": [1, 2, 3, 4, 5]
}' \
"${GITLAB_
URL}/api/v4/ai/d
uo_workflows/workflows"
{"message":"400 Bad request - 14:errors resolving d
uo-workflow-svc.staging.runway.gitlab.net: [field:hostname l
I tried dws-loadtest.staging.runway.gitlab.net first, then I tried d
uo-workflow-svc.staging.runway.gitlab.net problem just w
ith the new service. But I got the same error for both. 🤔
just incase there was a
My machine can resolve both.
Mark Lapierre2 weeks ago Maintainer
I think that happens when rails calls d
uo_workflow_token , but when I execute the service call from rails console, it works
Mark Lapierre2 weeks ago Maintainer
I get the same error whether I'm using G
DK on my machine, or the load test environment deployed in GCP.
Mark Lapierre2 weeks ago Maintainer
When I try to debug the error, it changes to:
"400 Bad request - 14:failed to connect to all addresses; last error: U
NAVA
I
LABLE: ipv4:**************:443
Mark Lapierre2 weeks ago Maintainer
staging.gitlab.com
is fine, but I assume it's using cloud.staging.gitlab.com as the DWS endpoint.
Yeah, confirmed staging is hitting d
uo-workflow-svc.staging.runway.gitlab.net via cloud.staging.gitlab.com in the cloud run
logs: https://cloudlogging.app.goo.gl/
bnicZPkX6zcW5Y
qLA
Edited 2 weeks ago by Mark Lapierre
Mark Lapierre2 weeks ago Maintainer
It's work
ing now on the load test machine.
Something is still wrong w
ith my G
DK
but I can leave that for another day.
Reply…
Mark Lapierrementioned in merge request gitlab-org/
modelops/applied-
l/code-suggestions/ai
-assist!3504 (merged) 3 weeks ago
Mark Lapierrementioned in issue gitlab-org/
modelops/applied-
m
l/code-suggestions/ai
-assist#
2
44
1
(closed) 3 weeks ago
Brittany Wilkerson added group ai framework
label and removed group performance enablement
label 3 weeks ago
m
Brittany Wilkerson added group performance enablement label and removed group ai framework
Mark Lapierre 3 weeks ago There's a problem
w
-
loadtest/
/jobs/
585369
93
11
1
ith the new Runway service. Deployment fails: https://gitlab.com
/gitlab-com
label 3 weeks ago
/gl-
infra/platform
Maintainer
/runway/deployments/dws-
│ Error: Error creating Service: googleapi: Error 400: traffic[].percent: traffic percentage adds to 0, should
│ Details:
│ [
│ {
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 17/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
│ "@type": "type.googleapis.com/google.rpc.BadRequest",
│ "fieldViolations": [
│ {
Open Confidential
Agentic AI performance test execution and data gathering
│ "description": "traffic percentage adds to 0, should be 100",
│ "field": "traffic[].percent"
│ }
│ ]
│ }
│ ]
│
│ with google
_cloud
_run_v2_service.[MASKED]_service["us-east1"],
│ on main.tf line 63, in resource "google
_cloud
_run_v2_service" "[MASKED]_service":
│ 63: resource "google
_cloud
_run_v2_service" "[MASKED]_service" {
│
╵
Error: failed to execute terraform command: exit status 1
Error: Deploy(): terraformApply(): executing "/opt/[MASKED]/bin/[MASKED]ctl" failed: exit status 1
The problem
is that the initial deployment failed a healthcheck: https://cloudlogging.app.goo.gl/388YQJZB6Ci6k6V97
Ready condition status changed to False for Revision dws-loadtest-k
mxf w
ith message: The user-provided container failed to start and
listen on the port defined provided
by the PORT=8080 environment variable w
ithin the allocated ti
meout. This can happen when the
container port is m
isconfigured or if the ti
meout is too short. The health check ti
meout can be extended. Logs for this revision m
ight
contain more information.
Mark Lapierre 3 weeks ago Strange that it's check
ing on port 8080 . That's not configured anywhere:
Maintainer
https://gitlab.com
ref_type=heads
https://gitlab.com
ref_type=heads
/gitlab-org/
modelops/applied-
l/code-suggestions/ai
-
-assist/
/
blob
/
main/.runway/dws-loadtest/env-staging.ym
l?
/gitlab-org/
modelops/applied-
l/code-suggestions/ai
-
-assist/
/
blob
/
main/.runway/dws-loadtest/runway.ym
l?
Mark Lapierre 3 weeks ago O
IC. 8080 is the Runway default: https://docs.runway.gitlab.com
Maintainer
/runti
mes/cloud-run/onboarding/#update-dockerfile
Mark Lapierre 3 weeks ago So why do we need to override the default, but the main DWS config didn't?
Maintainer
https://gitlab.com
/gitlab-org/
modelops/applied-
staging.ym
l?ref_type=heads
m
l/code-suggestions/ai
-
-assist/
/
blob
/
main/.runway/duo-workflow-svc/runway-
Mark Lapierre 3 weeks ago Ask
ing in Slack: https://gitlab.slack.com
Maintainer
/archives/C05G970PHS
A
/p
17594556
9
36229
1
1
Edited 3 weeks ago by Mark Lapierre
Gonzalo Servat2 weeks ago Developer
@wortschi @
m
lapierre apologies for the delay in responding to this (and Slack
). I
was off Friday/Monday then I started EOC on
Tuesday.
As you've noted, yes port back 60 days).
8080 is indeed the default port. I can't actually see any logs for your service though, which is odd (I
went
The reason for that "traffic percentage adds to 0, should
instance: https://gitlab.com
/gitlab-com
/gl-
infra/platform
be
1
00" error is because your service failed to deploy successfully in the first
-
/runway/deployments/dws-loadtest/
/jobs/
6
4636
4
111
1
1
This is why we recommend bootstrapping your service before trying to run your own app.
I'm going to try deleting your Runway service and attempt to re-deploy to see what happens.
Edited 2 weeks ago by Gonzalo Servat
Gonzalo Servat2 weeks ago Developer
🤔 I'm not sure what has changed
between then and now, but after deleting the Cloud Run service and re-deploying, it worked fine:
https://gitlab.com
/gitlab-com
/gl-
infra/platform
-
/runway/deployments/dws-loadtest/
/jobs/
6560
11
1
3459
m
m
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 18/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
https://dws-loadtest.staging.runway.gitlab.net
🤷
Open Confidential
Agentic AI performance test execution and data gathering
Edited 2 weeks ago by Gonzalo Servat
Mark Lapierre2 weeks ago Maintainer
Thanks @gsgl ! 🙇
Reply…
Martin Wortschack
mentioned in issue gitlab-org/gitlab
#570203
1 month ago
Mark Lapierre
1 month ago Maintainer
I started redeploying the test environment, follow
ing the instructions in https://gitlab.com
/gitlab-org/quality/gitlab-environment-toolk
it-
-
configs/performance-test-rfh/
/tree/load-test-agentic-chat/configs/load-test-agentic-chat?ref_type=heads#how-to-deploy
make deploy load-test-agentic-chat failed:
│ Error: Unsupported Terraform Core version
│
│
│ on /Users/<USER>/gitlab-org/gitlab-environment-toolkit/terraform/mod
ules/gitlab_
│ 2: required
_version = ">= 1.12"
gcp_service
_account/main.t
│ Mod
ule mod
ule.gitlab_ref
_arch_
gcp.mod
ule.postgres.mod
ule.google
_service
_account (from ../gitlab_
gcp_servi
│ 1.9.0. To proceed, either choose another supported Terraform version or update this version constraint. Ve
│ reason, so updating the constraint may lead to other errors or unexpected behavior.
fix: edited .tool-versions w
failed again
ith terraform 1.12.0
Terraform already initialized
Validating terraform formatting...
Validating terraform syntax...
╷
│
│
│ Error: registry.terraform.io/hashicorp/google: there is no package for registry.terraform.io/hashicorp/goo
╵
make: *** [Makefile:65: validate] Error 1
fix: (cd /Users/<USER>/gitlab-org/gitlab-environment-toolkit/terraform/environments/load-test-agentic-chat && terraform
init)
make configure load-test-agentic-chat failed:
fatal: [agenchat-gitlab-rails-3]: U
NREACHABLE! => changed=false
msg: |-
Failed to connect to the host via ssh: @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
@ WARN
I
NG: U
NPROTECTED PRIVATE KEY FI
LE! @
@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
Permissions 0755 for '/Users/<USER>/gitlab-org/gitlab-environment-toolkit/ansible/../keys/load-test-agenti
It is required that your private key files are
NOT accessible by others.
This private key will be ignored.
Load key "/Users/<USER>/gitlab-org/gitlab-environment-toolkit/ansible/../keys/load-test-agentic-chat_ssh_
sa_114127881498200044909@*************: Permission denied (publickey).
unreachable: true
fix: chmod 600 /Users/<USER>/gitlab-org/gitlab-environment-toolkit/ansible/../keys/load-test-agentic-chat_ssh_key
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 19/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
One more error(/
warning?):
Open Confidential
TASK [common : Check if GitLab Secrets file exists] ********************************************************
Agentic AI performance test execution and data gathering
fatal: [agenchat-consul-1]: FA
I
LED! =>
msg: '
Unable to execute ssh command line on a controller d
ue to: [Errno 24] Too many open files'
PLAY RECAP *************************************************************************************************
agenchat-consul-1 : ok=24 changed=10 unreachable=0 failed=1 skipped=57 rescued=0 ig
I'm out of ti
me for today so I tore it down. Will redeploy early tomorrow
Brittany Wilkerson
1 month agoAuthor Developer
@
lapierre For the last error:
m
https://gitlab.com
/gitlab-org/gitlab-environment-toolk
-
it/
/
blob
/
main/docs/environment_troubleshooting.md#unable-to-execute-ssh-
command-line-on-a-controller-due-to-errno-2
4-too-
many-open-files
Brittany Wilkerson
1 month agoAuthor Developer
Been review
ing the errors on this also to see if we m
issed anything in putting the environment together that it caused a b
it of difficulty
on the reboot.
1. I'm guessing GET released an update between creation and re-deployment, hence the Terraform version m
ismatch
2. The Terraform error in
1
caused the .terraform folder to get created (but not fully initiali
ze) and then 2 happened
because it sees
the .terraform folder and sk
ips initiali
zation the second ti
me.
3. Perm
issions errors on the ssh keys. I thought we'd fixed that but maybe it didn't get pushed right, and maybe the make script
should just take care of it.
4. The threads error is a thing that pops up in GET on occasion, not sure if we can m
itigate it at all. We probably didn't run into it
ourselves because we're using our GET installs where we've increased threads when we ran into it in the past.
Mark Lapierre
1 month ago Maintainer
Thanks @bw
ilkerson
3 , no issues w
1
ith deploy and configure today (after setting forks = 20 )!
Mark Lapierre
1 month ago Maintainer
@bw
ilkerson
1
3 I ran into trouble when running make add-runners load-test-agentic-chat :
╷
│
│ Error: Error creating the custom project role projects/gitlab-qa-ai-latency-baseline/roles/agenchat_runn
│ with mod
ule.runner_
deployment.mod
ule.runner.google
_project_iam_custom_role.runner_manager,
│ on .terraform/mod
ules/runner_
deployment/mod
ules/google/runner/iam.tf line 5, in resource "google
│ 5: resource "google
_project_iam_custom_role" "runner_manager" {
_proje
│
╵
╷
│
│ Error: Error creating SecretCiphertext: googleapi: Error 403: Permission 'cloudkms.cryptoK
eyVersions.use
│ with mod
ule.runner_
deployment.mod
ule.runner.google
_kms_secret_ciphertext.runner_token,
│ on .terraform/mod
ules/runner_
deployment/mod
ules/google/runner/secrets.tf line 12, in resource "google
│ 12: resource "google
_kms_secret_ciphertext" "runner_token" {
_
│
╵
I figured I needed to make setup_service
_account_
grit_role , but I'm not allowed:
gcloud iam roles create GRITProvisioner --project=gitlab-qa-ai-latency-baseline --file=./configs/load-test
ERROR: (gcloud.iam.roles.create) [<EMAIL>] does not have permission to access projects instan
Mark Lapierre
1 month ago Maintainer
Oh wait, new shell session, need to export the env vars again...
Mark Lapierre
1 month ago Maintainer
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 20/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
Not quite... Now
I get:
Open Confidential
╷
Agentic AI performance test execution and data gathering
│ Error: Error creating the custom project role projects/gitlab-qa-ai-latency-baseline/roles/agenchat_runn
│
│ with mod
ule.runner_
deployment.mod
ule.runner.google
_project_iam_custom_role.runner_manager,
│ on .terraform/mod
ules/runner_
deployment/mod
ules/google/runner/iam.tf line 5, in resource "google
_proje
│ 5: resource "google
_project_iam_custom_role" "runner_manager" {
│
╵
╷
│ Error: Error creating the custom project role projects/gitlab-qa-ai-latency-baseline/roles/agenchat_runn
│
│ with mod
ule.runner_
deployment.mod
ule.fleeting.mod
ule.gce[0].google
_project_iam_custom_role.instance
gr
_
│ on .terraform/mod
ules/runner_
deployment/mod
ules/google/fleeting/gce/iam.tf line 5, in resource "google
│ 5: resource "google
_project_iam_custom_role" "instance
_
group_manager" {
│
╵
Mark Lapierre
1 month ago Maintainer
Looks li
ke this is right: https://stackoverflow.com
/a/78
00980
1
The role has been deleted, but between 7 and 44 days ago, so it can't be undeleted or recreated w
ith the same id.
Fix: added _v2 to the role names
Mark Lapierre
1 month ago Maintainer
Now
it shows the runner as registered, but not online:
Mark Lapierre
1 month ago Maintainer
Found in the logs in the runners manager:
Sep 23 04:54:04 agenchat-runners-runner-manager docker[1092]: PAN
IC: Failed to verify the runner. You may
Sep 23 04:54:04 agenchat-runners-runner-manager systemd[1]: gitlab-runner.service: Main process exited, co
Sep 23 04:54:04 agenchat-runners-runner-manager docker[1380]: Error response from daemon: No such containe
Sep 23 04:54:04 agenchat-runners-runner-manager systemd[1]: gitlab-runner.service: Control process exited,
Sep 23 04:54:04 agenchat-runners-runner-manager systemd[1]: gitlab-runner.service: Failed with result '
exi
Fix: sudo systemctl restart gitlab-runner.service 🤷
Brittany Wilkerson
1 month agoAuthor Developer
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 21/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
Hmm. The runner key issue was known, and the makefile rm-runner task should also call a script that rips those keys out of the
Terraform state before destroying so that they aren't set to delete. Then when you run add-runner those keys are i
mported (if they
already exist).
Open Confidential
Agentic AI performance test execution and data gathering
But they came up for you after the restart?
Reply…
Mark Lapierre added labels
devops ai
-powered group ai framework section data-scienceworkflow
in dev
1 month ago
Martin Wortschack
1 month ago Developer
@
m
lapierre Do you have capacity to pick this up next?
Mark Lapierre
1 month ago Maintainer
@wortschi
Sure, although gitlab-com
/gl-
infra/platform
/runway/team#589 (closed) takes priority as soon as a test instance is available,
right?
Martin Wortschack
@
lapierre Yes, that is correct
1 month ago Developer
m
Reply…
Mark Lapierre2
2025-08-29 Status update
months ago Maintainer
I haven't had much ti
me for this issue due several to evaluation-runner issues. I'm
of 2025-09-08
OOO next week so w
ill resume work on this in the week
A
Mark Lapierre2
months ago Maintainer
I
was planning to test against the new Runway service (gitlab-org/
modelops/applied-
l/code-suggestions/ai
-assist#
3
1
1
3 (closed)) but it's not
ready quite yet.
m
lso needed to add myself as a deployer: gitlab-com
/gl-
infra/platform
/runway/provisioner!983 (merged)
In the mean ti
me I'll see if I can add some multi
-step workflow tests to go w
ith the ones Brittany already added in https://gitlab.com
org/
modelops/applied-
l/code-suggestions/ai
-
-assist/
/tree/
main/performance_tests/stress_tests?ref_type=heads
/gitlab-
m
Mark Lapierrementioned in merge request gitlab-com
/gl-
infra/platform
/runway/provisioner!983 (merged)2
months ago
service-epic-status-automationmentioned in epic gitlab-org/quality#962
months ago
Brittany Wilkerson2
Environment URL: http://34.75.
1
89.207/
months ago Internal noteAuthor Developer
Grafana URL: http://34.75.
-
1
89.207/
/grafana/login
Login credentials for both can be pulled from the configs in the repository, using the instructions for git-crypt ansible/inventory/secrets.yml file and pull the respective root password + grafana passwords from there.
to decrypt the
John McDonnell2
months ago Internal note Maintainer
@wortschi / @
lapierre -
is there anything we can do to help out at this point or are we still waiting on other parts of the runway
service. I think we have fully scripted the deployment of this environment at this stage so I'm considering to shut it down temporarily
until the runway service is ready as it can be redeployed quickly when it's required.
m
Mark Lapierre2
months ago Internal note Maintainer
@john.mcdonnell you can shut it down for now. Once I'm done w
ith gitlab-org/
-
modelops/ai
-
model-validation-and-research/ai
evaluation/prompt-li
brary#764 (closed) I'll pick this up again and redeploy.
Reply…
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 22/23
10/29/25, 4:48 PM Agentic AI performance test execution and data gathering (#3794) · Issue · gitlab-org/quality/quality-engineering/team-tasks
months agoAuthor Developer
Brittany Wilkerson2
The environment is available!
Open Confidential
Agentic AI performance test execution and data gathering
The configs are available at https://gitlab.com
/gitlab-org/quality/gitlab-environment-toolk
-
it-configs/performance-test-rfh/
/tree/load-test-
agentic-chat/configs/load-test-agentic-chat?ref_type=heads , along w
ith instructions about how to deploy, access or destroy it (or the
runners) if you need to make any changes.
The tests were checked in to https://gitlab.com
/gitlab-org/
modelops/applied-
-
assist/
/tree/
main/performance_tests/stress_tests?ref_type=heads w
the command to run them.
-
l/code-suggestions/ai
ith instructions on setting up GPT, what flags are required to be set, and
As mentioned in this week's update, I am heading out on PTO, so John w
ill be your point of contact in case you have any questions or
problems.
m
Brittany Wilkersonmentioned in issue gitlab-com
/engineering-division/pto-coverage#
232
Mark Lapierre assigned to @
lapierre2
months ago
Brittany Wilkerson changed the description 3 months ago ·
Brittany Wilkerson changed the description 3 months ago ·
Brittany Wilkersonmentioned in epic gitlab-org/quality#
20
1
3 months ago
Brittany Wilkerson added gitlab-org/quality#
20
1 as parent epic 3 months ago
Brittany Wilkerson set status to New 3 months ago
(closed)2
months ago
m
https://gitlab.com/gitlab-org/quality/quality-engineering/team-tasks/-/issues/3794 23/23