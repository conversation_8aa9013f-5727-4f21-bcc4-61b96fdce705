# frozen_string_literal: true

RSpec.describe GDK::ClaudeConnector do
  let(:anthropic_api_url) { 'https://api.anthropic.com/v1/messages' }
  let(:prompt) { 'Explain how to resolve the following issues:' }
  let(:max_tokens) { 1024 }
  let(:model_name) { 'claude' }
  let(:api_key) { 'api_key' }
  let(:out) { GDK::Output }

  subject { described_class.new(out).call(prompt) }

  before do
    stub_env('ANTHROPIC_API_KEY', api_key)
  end

  describe 'call' do
    let(:api_key) { '<anthropic-api-key>' }
    let(:content) { "Try this.\n" }
    let(:mock_api_response) do
      {
        id: 'msg_123456789',
        type: 'message',
        role: 'assistant',
        content: [
          {
            type: 'text',
            text: 'Try this.'
          }
        ],
        model: model_name,
        stop_reason: 'end_turn',
        stop_sequence: nil,
        usage: {
          input_tokens: 100,
          output_tokens: 100
        }
      }
    end

    context 'when asking the LLM for help' do
      before do
        stub_request(:post, anthropic_api_url).to_return(body: mock_api_response.to_json)
      end

      it 'returns a helpful message' do
        expect(GDK::Output).to receive(:puts).with(content)
        expect(GDK::Output).to receive(:puts).with('The current conversation used 200 tokens so far.')
        stub_prompt('y', 'Was this answer helpful?')
        expect(GDK::Output).to receive(:puts).with('Great, we are happy you could resolve your problem!')

        subject
      end

      it 'receives a follow-up question' do
        expect(GDK::Output).to receive(:puts).with(content)
        expect(GDK::Output).to receive(:puts).with('The current conversation used 200 tokens so far.')
        stub_prompt('n', 'Was this answer helpful?')
        stub_prompt('How can I try it?', 'Please enter your follow-up question')
        expect(GDK::Output).to receive(:info).with('Processing your follow-up question ...')
        expect(GDK::Output).to receive(:puts).with(content)
        expect(GDK::Output).to receive(:puts).with('The current conversation used 200 tokens in total.')

        subject
      end
    end

    context 'when no API key is provided' do
      let(:api_key) { '' }

      it 'indicates the missing API key' do
        expect(GDK::Output).to receive(:warn).with('AI assistance for troubleshooting is missing the API key.')
        expect(GDK::Output).to receive(:info).with('Add the API key in your shell config `export ANTHROPIC_API_KEY=<your-anthropic-api-key>')

        subject
      end
    end
  end
end
